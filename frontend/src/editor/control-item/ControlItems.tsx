import React from "react";
import { Box, IconButton } from "@mui/material";
import { observer } from "mobx-react-lite";
import CloseIcon from "@mui/icons-material/Close";
import { useLayoutStore } from "../../store/store-context";
import { useEffect, useState } from "react";
import Presets from "./presets";
import Animations from "./animations";
import Smart from "./smart";
import BasicText from "./basic-text";
import BasicImage from "./basic-image";
import BasicVideo from "./basic-video";
import BasicAudio from "./basic-audio";
import BasicShape from "./basic-shape";
import CaptionText from "./caption-text";
import { StoreContext } from "../../store";

type ControlType = "text" | "image" | "video" | "audio" | "shape" | "caption";

interface ContainerProps {
  children: React.ReactNode;
}

const Container = observer(({ children }: ContainerProps) => {
  const layoutStore = useLayoutStore();
  const store = React.useContext(StoreContext);
  const [displayToolbox, setDisplayToolbox] = useState(false);

  useEffect(() => {
    setDisplayToolbox(!!layoutStore.activeToolboxItem);
  }, [layoutStore.activeToolboxItem]);

  // 检查是否有选中的元素或字幕
  const hasSelectedItem = store.selectedElement || store.getSelectedCaption();

  if (!layoutStore.controlsVisible || !hasSelectedItem) {
    return null;
  }

  return (
    <Box
      sx={{
        width: "280px",
        height: "calc(100% - 96px)",
        mt: 3,
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        right: layoutStore.activeToolboxItem ? "75px" : "-100%",
        transition: "right 0.25s ease-in-out",
        zIndex: 200,
        display: "flex",
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "100%",
          position: "relative",
          bgcolor: "grey.100",
          display: "flex",
          borderRadius: 4,
          boxShadow: 3,
        }}
      >
        <IconButton
          onClick={() => {
            setDisplayToolbox(false);
            layoutStore.setActiveToolboxItem(null);
          }}
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            width: 32,
            height: 32,
            color: "text.secondary",
          }}
        >
          <CloseIcon sx={{ fontSize: 16 }} />
        </IconButton>
        {children}
      </Box>
    </Box>
  );
});

interface ActiveControlItemProps {
  activeToolboxItem?: string;
  controlType?: ControlType;
}

export const ControlItem = observer(() => {
  const store = React.useContext(StoreContext);
  const layoutStore = useLayoutStore();
  const [controlType, setControlType] = useState<ControlType | null>(null);

  const componentMap = {
    text: {
      "basic-text": <BasicText element={store.selectedElement} />,
      "preset-text": <Presets element={store.selectedElement} />,
      animation: <Animations />,
      smart: <Smart />,
    },
    image: {
      "basic-image": <BasicImage element={store.selectedElement} />,
      animation: <Animations />,
      smart: <Smart />,
    },
    video: {
      "basic-video": <BasicVideo element={store.selectedElement} />,
      animation: <Animations />,
    },
    audio: {
      "basic-audio": <BasicAudio element={store.selectedElement} />,
      smart: <Smart />,
    },
    shape: {
      "basic-shape": <BasicShape element={store.selectedElement} />,
      animation: <Animations />,
    },
    caption: {
      "caption-text": <CaptionText />,
    },
  };

  const ActiveControlItem = observer(
    ({ activeToolboxItem, controlType }: ActiveControlItemProps) => {
      if (!activeToolboxItem || !controlType) return null;
      return componentMap[controlType]?.[activeToolboxItem] || null;
    }
  );

  useEffect(() => {
    const selectedCaption = store.getSelectedCaption();

    if (store.selectedElement) {
      const newControlType = store.selectedElement.type as ControlType;

      // 只有当 controlType 发生变化时，才重置 activeToolboxItem
      if (newControlType !== controlType) {
        setControlType(newControlType);
        const firstComponentKey = Object.keys(componentMap[newControlType])[0];
        layoutStore.setActiveToolboxItem(firstComponentKey);
      }
    } else if (selectedCaption) {
      // 如果选中了字幕
      const newControlType = "caption" as ControlType;

      if (newControlType !== controlType) {
        setControlType(newControlType);
        const firstComponentKey = Object.keys(componentMap[newControlType])[0];
        layoutStore.setActiveToolboxItem(firstComponentKey);
      }
    } else {
      // 没有选中任何元素或字幕
      setControlType(null);
      layoutStore.setActiveToolboxItem(null);
    }
  }, [store.selectedElement?.type, store.captions]);

  return (
    <Container>
      <ActiveControlItem
        controlType={controlType}
        activeToolboxItem={layoutStore.activeToolboxItem}
      />
    </Container>
  );
});
