import { Caption, CaptionStyle } from "../types";
import { ASS_CONFIG } from "../config";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

/**
 * ASS字幕工具类
 * 提供高级的ASS字幕生成和处理功能
 */
export class ASSSubtitleUtils {
  /**
   * 转换颜色格式：从 #RRGGBB 到 &HBBGGRR& (ASS格式)
   */
  static convertColorToASS(hexColor: string): string {
    if (!hexColor || hexColor === "transparent")
      return ASS_CONFIG.COLORS.TRANSPARENT;
    const hex = hexColor.replace("#", "");
    if (hex.length === 6) {
      const r = hex.substring(0, 2);
      const g = hex.substring(2, 4);
      const b = hex.substring(4, 6);
      return `&H${b}${g}${r}&`;
    }
    return ASS_CONFIG.COLORS.WHITE;
  }

  /**
   * 转换对齐方式到ASS格式
   */
  static getASSAlignment(textAlign: string): number {
    switch (textAlign) {
      case "left":
        return ASS_CONFIG.ALIGNMENT.LEFT;
      case "right":
        return ASS_CONFIG.ALIGNMENT.RIGHT;
      case "center":
      default:
        return ASS_CONFIG.ALIGNMENT.CENTER;
    }
  }

  /**
   * 转换时间格式到ASS格式
   */
  static convertTimeToASS(timeStr: string): string {
    const parts = timeStr.split(":");
    if (parts.length === 3) {
      const hours = parseInt(parts[0], 10);
      const minutes = parts[1];
      const seconds = parts[2];
      return `${hours}:${minutes}:${seconds}.00`;
    }
    return timeStr + ".00";
  }

  /**
   * 转义ASS文本
   */
  static escapeASSText(text: string): string {
    return text
      .replace(/\\/g, "\\\\")
      .replace(/\{/g, "\\{")
      .replace(/\}/g, "\\}")
      .replace(/\n/g, "\\N");
  }

  /**
   * 生成ASS样式字符串
   */
  static generateASSStyle(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    const isBold = style.styles.includes("bold") || style.fontWeight >= 700;
    const isItalic = style.styles.includes("italic");
    const isUnderline = style.styles.includes("underline");

    // 计算边距，考虑位置偏移
    let bottomMargin = canvasHeight
      ? Math.round(canvasHeight * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;
    const sideMargin = canvasWidth
      ? Math.round(canvasWidth * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;

    // 如果有位置偏移，调整底部边距
    if (style.positionY !== undefined && canvasHeight) {
      // positionY是相对于默认底部位置的偏移
      // 负值向上移动，正值向下移动
      bottomMargin = Math.max(0, bottomMargin - style.positionY);
    }

    return [
      style.fontFamily || ASS_CONFIG.DEFAULT_STYLE.fontFamily,
      style.fontSize || ASS_CONFIG.DEFAULT_STYLE.fontSize,
      this.convertColorToASS(style.fontColor),
      this.convertColorToASS(style.fontColor),
      this.convertColorToASS(style.strokeColor),
      this.convertColorToASS(style.backgroundColor),
      isBold ? 1 : 0,
      isItalic ? 1 : 0,
      isUnderline ? 1 : 0,
      0, // StrikeOut
      100, // ScaleX
      100, // ScaleY
      Math.round(style.charSpacing || 0),
      0, // Angle
      1, // BorderStyle
      Math.max(0, style.strokeWidth || 0),
      Math.max(0, style.shadowBlur || 0),
      this.getASSAlignment(style.textAlign),
      sideMargin,
      sideMargin,
      bottomMargin,
      1, // Encoding
    ].join(",");
  }

  /**
   * 生成完整的ASS文件内容
   */
  static generateASSContent(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    const defaultStyle: CaptionStyle = {
      ...ASS_CONFIG.DEFAULT_STYLE,
      styles: [...ASS_CONFIG.DEFAULT_STYLE.styles],
      gradientColors: [...ASS_CONFIG.DEFAULT_STYLE.gradientColors],
      fontSize: canvasHeight
        ? Math.max(
            ASS_CONFIG.MIN_FONT_SIZE,
            Math.round(canvasHeight * ASS_CONFIG.DEFAULT_FONT_SIZE_RATIO)
          )
        : ASS_CONFIG.DEFAULT_STYLE.fontSize,
    };

    const finalStyle: CaptionStyle = style
      ? { ...defaultStyle, ...style }
      : defaultStyle;

    // ASS文件头部
    let assContent = `[Script Info]
Title: Generated Subtitles
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: None
PlayResX: ${canvasWidth || 1920}
PlayResY: ${canvasHeight || 1080}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${this.generateASSStyle(finalStyle, canvasWidth, canvasHeight)}

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`;

    // 添加字幕事件
    captions.forEach((caption) => {
      const startTime = this.convertTimeToASS(caption.startTime);
      const endTime = this.convertTimeToASS(caption.endTime);
      let escapedText = this.escapeASSText(caption.text);

      // 添加位置控制
      if (
        (finalStyle.positionX !== undefined ||
          finalStyle.positionY !== undefined) &&
        canvasWidth &&
        canvasHeight
      ) {
        // 计算水平位置
        let xPos = canvasWidth / 2; // 默认居中
        if (finalStyle.originX === "left") {
          xPos = 0;
        } else if (finalStyle.originX === "right") {
          xPos = canvasWidth;
        }
        if (finalStyle.positionX !== undefined) {
          xPos += finalStyle.positionX;
        }

        // 计算垂直位置
        let yPos =
          canvasHeight - canvasHeight * ASS_CONFIG.DEFAULT_MARGIN_RATIO; // 默认底部位置
        if (finalStyle.originY === "top") {
          yPos = 0;
        } else if (finalStyle.originY === "center") {
          yPos = canvasHeight / 2;
        }
        if (finalStyle.positionY !== undefined) {
          yPos += finalStyle.positionY;
        }

        // 添加位置标签（ASS格式需要同时指定X和Y坐标）
        escapedText = `{\\pos(${Math.round(xPos)},${Math.round(
          yPos
        )})}${escapedText}`;
      }

      // 添加渐变效果
      if (
        finalStyle.useGradient &&
        finalStyle.gradientColors &&
        finalStyle.gradientColors.length >= 2
      ) {
        const color1 = this.convertColorToASS(finalStyle.gradientColors[0]);
        const color2 = this.convertColorToASS(finalStyle.gradientColors[1]);
        escapedText = `{\\1c${color1}\\3c${color2}}${escapedText}`;
      }

      // 添加阴影效果
      if (finalStyle.shadowOffsetX !== 0 || finalStyle.shadowOffsetY !== 0) {
        escapedText = `{\\shad${Math.max(
          1,
          Math.abs(finalStyle.shadowOffsetX || 0)
        )}}${escapedText}`;
      }

      assContent += `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,${escapedText}\n`;
    });

    return assContent;
  }

  /**
   * 创建ASS字幕文件
   */
  static createASSFile(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!captions || captions.length === 0) {
      return "";
    }

    const assContent = this.generateASSContent(
      captions,
      style,
      canvasWidth,
      canvasHeight
    );

    // 创建临时文件
    const tempDir = os.tmpdir();
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .replace("Z", "");

    const subtitlePath = path.join(tempDir, `ass_subtitles_${timestamp}.ass`);

    fs.writeFileSync(subtitlePath, assContent, "utf8");
    console.log("Generated ASS subtitle file:", subtitlePath);

    return subtitlePath;
  }

  /**
   * 验证ASS文件格式
   */
  static validateASSFile(filePath: string): boolean {
    try {
      const content = fs.readFileSync(filePath, "utf8");
      return (
        content.includes("[Script Info]") &&
        content.includes("[V4+ Styles]") &&
        content.includes("[Events]")
      );
    } catch (error) {
      console.error("Error validating ASS file:", error);
      return false;
    }
  }

  /**
   * 获取ASS文件的字幕数量
   */
  static getSubtitleCount(filePath: string): number {
    try {
      const content = fs.readFileSync(filePath, "utf8");
      const dialogueLines = content
        .split("\n")
        .filter((line) => line.startsWith("Dialogue:"));
      return dialogueLines.length;
    } catch (error) {
      console.error("Error counting subtitles:", error);
      return 0;
    }
  }
}
